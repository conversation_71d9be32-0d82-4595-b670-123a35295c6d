#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import asyncio
import json
import ssl
import websockets
import getpass
import keyring
import re
from typing import Any, Dict, List, Optional, Tuple
from pathlib import Path
from datetime import datetime
# Try optional Questionary for multi-select deletion
try:
    import questionary  # type: ignore
    HAS_QUESTIONARY = True
except Exception:
    questionary = None  # type: ignore
    HAS_QUESTIONARY = False

# ====================== SETTINGS ======================
XO_HOST = "*************"            # XO host (no scheme)
XO_USER = "<EMAIL>"
KEYRING_SERVICE = "xen_orchestra"

# Filter templates by prefix; set to "" for all
NAME_PREFIX = "deploy-"  # <--- Changed this line

# Deployment defaults moved to top
DEFAULT_VCPUS = 4
DEFAULT_RAM_GIB = 4

# Where we persist the last-used config
CHECKPOINT_PATH = Path("vm_deploy_checkpoint.json")
# Default local Jinja2 cloud-config template path (relative to this script)
DEFAULT_J2_PATH = (Path(__file__).resolve().parent / "config_template" / "cloud-init-admin-host.yaml.j2")
# Always use this fixed J2 template path (no prompt unless missing)
FIXED_J2_PATH = Path("/mnt/c/Users/<USER>/OneDrive/Documents/GithubWerk-AWS-Samples/xen_orchestra/xoa/config_template/cloud-init-admin-host.yaml.j2")

# Never delete these VM IDs
PROTECTED_VM_IDS = {
    "d52f6352-2d6d-874c-1010-9da4785e169b",  # XOA
    "6e520543-d0a2-66f7-23c4-a6a7be9b7085",  # lee
}

def confirm(prompt: str) -> bool:
    """Safely ask for confirmation, requiring full input to prevent accidental key press issues."""
    while True:
        response = input(f"{prompt} [y/n]: ").strip().lower()
        if response in ("y", "yes"):
            return True
        elif response in ("n", "no"):
            return False
        print("Please enter 'y' or 'n'.")

# ====================== UTILITIES ======================
def ask(prompt: str, default: Optional[str] = None) -> str:
    # Use full input() to get the entire line, not just first keypress
    s = input(f"{prompt}{f' [{default}]' if default is not None else ''}: ").strip()
    # Only return default if the user pressed Enter without typing anything
    return s if s else (default or "")

def ask_int(prompt: str, default: int) -> int:
    s = input(f"{prompt} [{default}]: ").strip()
    return int(s) if s else default

def bytes_from_gib(n: int) -> int:
    return int(n * (1024 ** 3))

def ask_path(prompt: str, default: Path) -> Path:
    """Ask for a filesystem path; accept Enter, 'y', or 'yes' to use the default."""
    s = input(f"{prompt} [{default}]: ").strip()
    if s == "" or s.lower() in ("y", "yes"):
        s = str(default)
    return Path(s).expanduser()

def term_pick(items: List[Any], label_fn, title: str, allow_skip: bool = False) -> Optional[Any]:
    print(f"\n{title}")
    if not items:
        print("  (none)")
        return None
    for i, it in enumerate(items, 1):
        print(f"  {i}. {label_fn(it)}")
    prompt = f"Select (1-{len(items)})"
    if allow_skip:
        prompt += " or Enter to skip"
    prompt += ", or 'q' to cancel: "
    while True:
        s = input(prompt).strip().lower()
        if s in ("q", "quit"):
            return None
        if allow_skip and s == "":
            return None
        try:
            idx = int(s)
            if 1 <= idx <= len(items):
                return items[idx - 1]
        except Exception:
            pass
        print("Invalid choice.")

def extract_j2_defaults(template_path: Path) -> Dict[str, Optional[str]]:
    try:
        content = template_path.read_text()
    except Exception:
        return {}
    defaults = {}
    for var in ['hostname', 'fqdn', 'username', 'password']:
        # Look for patterns like {{ var | default('value') }} anywhere in the content
        # Handle both single and double quotes, and optional whitespace
        # Build regex pattern to match {{ var | default('value') }}
        var_escaped = re.escape(var)
        # Pattern parts: {{ var | default('value') }}
        start = r"{{\s*"
        middle = r"\s*\|\s*default\(\s*"
        quotes = r"['\"]([^'\"]*)['\"]"
        end = r"\s*\)\s*}}"
        pattern = start + var_escaped + middle + quotes + end
        match = re.search(pattern, content)
        if match:
            defaults[var] = match.group(1)
    return defaults

def strip_prefix(s: str, prefix: str) -> str:
    """Return s without the leading prefix (case-insensitive) if present."""
    if not s or not prefix:
        return s
    return s[len(prefix):] if s.lower().startswith(prefix.lower()) else s

async def multi_pick(items: List[Any], label_fn, title: str) -> List[Any]:
    """Multi-select using Questionary (in a thread) when available; fallback to manual input."""
    if HAS_QUESTIONARY:
        def _ask_checkbox():
            choices = [questionary.Choice(title=label_fn(it), value=it) for it in items]  # type: ignore[attr-defined]
            return questionary.checkbox(title, choices=choices).ask()  # type: ignore[attr-defined]
        try:
            selected = await asyncio.to_thread(_ask_checkbox)
            return selected or []
        except Exception as e:
            print(f"⚠ Questionary unavailable here: {e}. Falling back to manual input.")

    # Fallback: comma-separated indexes
    print(f"\n{title}")
    if not items:
        print("  (none)")
        return []
    for i, it in enumerate(items, 1):
        print(f"  {i}. {label_fn(it)}")
    while True:
        s = input("Select indexes (e.g., 1,3,5) or 'q' to cancel: ").strip().lower()
        if s in ("q", "quit", ""):
            return []
        try:
            idxs = sorted({int(x) for x in re.split(r"[,\s]+", s) if x})
            picked = [items[i - 1] for i in idxs if 1 <= i <= len(items)]
            if picked:
                return picked
        except Exception:
            pass
        print("Invalid selection. Try again.")

# ====================== CLASS 1: CLIENT ======================
class XOClient:
    """Handles connection, auth, and JSON-RPC calls to Xen Orchestra."""
    def __init__(self, host: str, user: str, keyring_service: str):
        self.host = host
        self.user = user
        self.keyring_service = keyring_service
        self.ws: Optional[websockets.WebSocketClientProtocol] = None
        self.ssl_ctx = ssl._create_unverified_context()  # XO often uses self-signed certs
        self._next_id = 1  # RPC id counter

    async def __aenter__(self):
        await self.connect()
        await self.sign_in()
        return self

    async def __aexit__(self, exc_type, exc, tb):
        await self.close()

    async def connect(self):
        uri = f"wss://{self.host}/api/"
        self.ws = await websockets.connect(uri, ssl=self.ssl_ctx)

    async def close(self):
        if self.ws:
            await self.ws.close()
            self.ws = None

    async def call(self, method: str, params: Optional[Dict[str, Any]] = None) -> Any:
        """
        Robust JSON-RPC: assign a unique id, send, then read frames until we get
        the response with our id. Ignore push/event frames like {'method':'all',...}.
        """
        if not self.ws:
            raise RuntimeError("WebSocket not connected.")

        rpc_id = self._next_id
        self._next_id += 1

        payload = {"id": rpc_id, "jsonrpc": "2.0", "method": method, "params": params or {}}
        await self.ws.send(json.dumps(payload))

        while True:
            raw = await self.ws.recv()
            try:
                resp = json.loads(raw)
            except Exception:
                # Unknown frame, keep waiting
                continue

            # Event/push frames (e.g., task updates)
            if isinstance(resp, dict) and "method" in resp and resp.get("method") == "all":
                continue

            # We only care about the frame matching our id
            if isinstance(resp, dict) and resp.get("id") == rpc_id:
                if "error" in resp:
                    raise RuntimeError(f"{method} failed: {resp['error']}")
                if "result" in resp:
                    return resp["result"]
                raise RuntimeError(f"{method} unexpected response: {resp}")

            # Frames for other in-flight calls: ignore
            continue

    async def sign_in(self):
        """
        Sign in using keyring-stored password if available.
        If XO returns code 3 (auth failed), clear keyring and re-prompt.
        """
        pw = keyring.get_password(self.keyring_service, self.user)
        first_attempt = True
        while True:
            if not pw:
                pw = getpass.getpass(f"Password for {self.user}: ")
            try:
                await self.call("session.signIn", {"email": self.user, "password": pw})
                print(f"✅ Logged in as {self.user}")
                # Save to keyring if not already
                if first_attempt and keyring.get_password(self.keyring_service, self.user) is None:
                    keyring.set_password(self.keyring_service, self.user, pw)
                    print("🔑 Password saved to system keyring.")
                return
            except RuntimeError as e:
                msg = str(e)
                bad_creds = ("'code': 3" in msg) or ('"code": 3' in msg)
                if bad_creds:
                    try:
                        keyring.delete_password(self.keyring_service, self.user)
                    except keyring.errors.PasswordDeleteError:
                        pass
                    print("❌ Invalid credentials. Please re-enter password.")
                    pw = None
                    first_attempt = False
                    continue
                raise  # other errors bubble up

# ====================== CLASS 2: INVENTORY ======================
class Inventory:
    """Loads the XO object graph, filters templates, and resolves VIFs for a template."""
    def __init__(self, client: XOClient):
        self.client = client
        self.objects: Dict[str, Dict[str, Any]] = {}

    async def refresh(self):
        self.objects = await self.client.call("xo.getAllObjects")

    def list_templates(self, prefix: str = "") -> List[Dict[str, Any]]:
        temps = [o for o in self.objects.values() if o.get("type") == "VM-template"]
        if prefix:
            pre = prefix.lower()
            temps = [t for t in temps if (t.get("name_label") or "").lower().startswith(pre)]
        return sorted(temps, key=lambda t: (t.get("name_label") or "").lower())

    def list_vms(self) -> List[Dict[str, Any]]:
        vms = [o for o in self.objects.values() if o.get("type") == "VM" and not o.get("is_a_template") and not o.get("is_a_snapshot")]
        return sorted(vms, key=lambda v: (v.get("name_label") or "").lower())

    def get_network_label(self, net_id: Optional[str]) -> str:
        if not net_id:
            return ""
        net = self.objects.get(net_id)
        return net.get("name_label") if net else (net_id or "")

    def resolve_template_vifs(self, template_obj: Dict[str, Any]) -> List[Dict[str, str]]:
        """
        Return VIFs for vm.create: [{'network': '<id: str>', 'mac': '<mac?>'}, ...]
        Handle both 'network' and '$network' ref styles.
        """
        res: List[Dict[str, str]] = []
        for vif_id in template_obj.get("VIFs", []):
            vif = self.objects.get(vif_id)
            if vif and vif.get("type") == "VIF":
                net_id = vif.get("network") or vif.get("$network")
                entry: Dict[str, str] = {"network": str(net_id) if net_id else ""}
                mac = vif.get("MAC")
                if mac:
                    entry["mac"] = mac
                res.append(entry)
        return res

# ====================== CLASS 3: CONFIG REPO ======================
class ConfigRepo:
    """Fetches saved configs (cloudConfig.getAll) and classifies user vs network."""
    def __init__(self, client: XOClient):
        self.client = client

    @staticmethod
    def _classify_text(text: str) -> str:
        lower = (text or "").strip().lower()
        if lower.startswith("#cloud-config"):
            return "cloud-config"
        if lower.startswith("#network") or lower.startswith("#network-config"):
            return "network-config"
        if "network:" in lower and ("version: 2" in lower or any(k in lower for k in ("ethernets:", "vlans:", "bonds:", "bridges:", "wifis:"))):
            return "network-config"
        if "version:" in lower and "config:" in lower and any(k in lower for k in ("type: dhcp", "type: static", "routes:", "nameserver", "address:", "subnets:")):
            return "network-config"
        return "cloud-config"

    async def fetch(self) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
        """Returns (user_configs, network_configs). Each item has id,name,template,type."""
        configs = await self.client.call("cloudConfig.getAll", {})
        user, net = [], []
        for c in configs:
            name = c.get("name", c.get("name_label", "unnamed"))
            template_text = c.get("template", "") or ""
            ctype = self._classify_text(template_text)
            item = {"id": c.get("id"), "name": name, "template": template_text, "type": ctype}
            (user if ctype == "cloud-config" else net).append(item)
        user.sort(key=lambda x: x["name"].lower())
        net.sort(key=lambda x: x["name"].lower())
        return user, net

    @staticmethod
    def find_by_id(configs: List[Dict[str, Any]], config_id: str) -> Optional[Dict[str, Any]]:
        for c in configs:
            if c.get("id") == config_id:
                return c
        return None

# ====================== CLASS 4: DEPLOYER ======================
class VMDeployer:
    """Handles checkpoint, prompts, summary, vm.create, and auto-start."""
    def __init__(self, client: XOClient, inventory: Inventory, repo: ConfigRepo):
        self.client = client
        self.inventory = inventory
        self.repo = repo

    # --------- Checkpoint helpers ---------
    def _save_checkpoint(self, data: Dict[str, Any]) -> None:
        try:
            CHECKPOINT_PATH.write_text(json.dumps(data, indent=2))
        except Exception as e:
            print(f"⚠ Could not save checkpoint: {e}")

    def _save_checkpoint_backup(self, data: Dict[str, Any]) -> None:
        """Write a timestamped backup of the checkpoint for easy rollback/edits."""
        try:
            ts = datetime.now().strftime("%Y%m%d-%H%M%S")
            backup = CHECKPOINT_PATH.with_name(f"{CHECKPOINT_PATH.stem}.{ts}{CHECKPOINT_PATH.suffix}")
            backup.write_text(json.dumps(data, indent=2))
            print(f"💾 Checkpoint backup saved: {backup.name}")
        except Exception as e:
            print(f"⚠ Could not save checkpoint backup: {e}")

    def _load_checkpoint(self) -> Optional[Dict[str, Any]]:
        if not CHECKPOINT_PATH.exists():
            return None
        try:
            return json.loads(CHECKPOINT_PATH.read_text())
        except Exception as e:
            print(f"⚠ Could not read checkpoint: {e}")
            return None

    @staticmethod
    def _extract_id(result) -> str:
        """XO may return {'id': '<uuid>'} or just '<uuid>'."""
        if isinstance(result, dict) and "id" in result:
            return str(result["id"])
        return str(result)

    # --------- Helpers: local J2 rendering ---------
    def _render_j2_cloud_config(self, template_path: Path, values: Dict[str, str]) -> str:
        if Environment is None:
            raise RuntimeError("Jinja2 is required. Install with: pip install jinja2")
        env = Environment(
            loader=FileSystemLoader(str(template_path.parent)),
            undefined=StrictUndefined,
            trim_blocks=True,
            lstrip_blocks=True,
        )
        try:
            tpl = env.get_template(template_path.name)
            return tpl.render(**values)
        except TemplateError as e:
            raise RuntimeError(f"Failed to render template {template_path}: {e}")

    # --------- Main flow ---------
    async def run(self):
        # Refresh objects
        await self.inventory.refresh()

        # Load saved configs once
        user_configs, network_configs = await self.repo.fetch()

        # Try checkpoint
        ckpt = self._load_checkpoint()
        use_ckpt = False
        if ckpt:
            print("\n💾 Found previous configuration checkpoint.")
            use_ckpt = ask("Use previous configuration? [Y/n]", "Y").lower() in ("y", "yes")

        if use_ckpt:
            # Resolve template from checkpoint
            template_id = ckpt.get("template_id")
            tmpl = self.inventory.objects.get(template_id)
            if not tmpl:
                print("⚠ Template in checkpoint not found; falling back to selection.")
                tmpl = await self._select_template()
                if not tmpl:
                    print("Cancelled.")
                    return
                template_id = tmpl["id"]
            else:
                print(f"📦 Using template from checkpoint: {tmpl.get('name_label')} ({template_id})")

            # VM Name (still editable)
            default_vm_from_template = strip_prefix(tmpl.get("name_label", "vm-from-template") or "", NAME_PREFIX)
            default_vm_name = ckpt.get("vm_name") or default_vm_from_template or "vm-from-template"
            vm_name = ask("VM Name", default_vm_name)
            vm_desc = vm_name  # description = name

            # CPUs/RAM from checkpoint
            vcpus = int(ckpt.get("vcpus", DEFAULT_VCPUS))
            ram_gib = int(ckpt.get("ram_gib", DEFAULT_RAM_GIB))
            memory_bytes = bytes_from_gib(ram_gib)

            # Network config from checkpoint (optional)
            net_cfg = None
            net_cfg_id = ckpt.get("network_config_id")
            if net_cfg_id:
                net_cfg = self.repo.find_by_id(network_configs, net_cfg_id)
                if not net_cfg:
                    print("⚠ Network config in checkpoint not found; skipping.")

            # Always use fixed J2 path; prompt only if missing
            tpl_path = FIXED_J2_PATH
            if not tpl_path.exists():
                print(f"⚠ Fixed J2 template not found at {tpl_path}. Please provide a valid path.")
                tpl_path = ask_path("Path to J2 template", DEFAULT_J2_PATH)
                while not tpl_path.exists():
                    print(f"Template not found: {tpl_path}")
                    tpl_path = ask_path("Path to J2 template", DEFAULT_J2_PATH)
            tpl_path = tpl_path.resolve()
            print(f"🧩 Using J2 template: {tpl_path}")
            j2_defaults = extract_j2_defaults(tpl_path)

            hostname = ask("Hostname", (ckpt.get("hostname") or j2_defaults.get("hostname") or vm_name))
            fqdn = f"{hostname}.local"
            username = ask("Username", (ckpt.get("username") or j2_defaults.get("username") or "admin"))

            password = getpass.getpass(f"Password for {username}: ")
            values = {"hostname": hostname, "fqdn": fqdn, "username": username, "password": password}
            cloud_config_text = self._render_j2_cloud_config(tpl_path, values)
            user_cfg_name = f"J2: {tpl_path}"

            # Build and save updated checkpoint + backup
            ckpt_payload: Dict[str, Any] = {
                "template_id": template_id,
                "vm_name": vm_name,
                "vcpus": vcpus,
                "ram_gib": ram_gib,
                "use_j2": True,
                "j2_path": str(tpl_path),
                "hostname": hostname,
                "username": username,
                "user_config_id": None,
                "network_config_id": (net_cfg.get("id") if net_cfg else None),
            }
            self._save_checkpoint(ckpt_payload)
            self._save_checkpoint_backup(ckpt_payload)

            # Rebuild VIFs from the (current) template
            vifs = self.inventory.resolve_template_vifs(tmpl)
            await self._summary_and_deploy(
                tmpl, template_id, vm_name, vm_desc, vcpus, ram_gib, memory_bytes,
                vifs, user_cfg_name, cloud_config_text, net_cfg
            )

        else:
            # Fresh interactive flow
            tmpl = await self._select_template()
            if not tmpl:
                print("Cancelled.")
                return
            template_id = tmpl["id"]
            default_vm_name = strip_prefix(tmpl.get("name_label", "vm-from-template") or "", NAME_PREFIX)
            vm_name = ask("VM Name", default_vm_name or "vm-from-template")
            vm_desc = vm_name

            vcpus = ask_int("vCPUs", DEFAULT_VCPUS)
            ram_gib = ask_int("RAM GiB", DEFAULT_RAM_GIB)
            memory_bytes = bytes_from_gib(ram_gib)

            # Always use fixed J2 template; prompt only if missing
            tpl_path = FIXED_J2_PATH
            if not tpl_path.exists():
                print(f"⚠ Fixed J2 template not found at {tpl_path}. Please provide a valid path.")
                tpl_path = ask_path("Path to J2 template", DEFAULT_J2_PATH)
                while not tpl_path.exists():
                    print(f"Template not found: {tpl_path}")
                    tpl_path = ask_path("Path to J2 template", DEFAULT_J2_PATH)
            tpl_path = tpl_path.resolve()
            j2_defaults = extract_j2_defaults(tpl_path)

            hostname = ask("Hostname", (j2_defaults.get("hostname") or vm_name))
            fqdn = f"{hostname}.local"
            username = ask("Username", (j2_defaults.get("username") or "admin"))

            password = getpass.getpass(f"Password for {username}: ")
            values = {"hostname": hostname, "fqdn": fqdn, "username": username, "password": password}
            cloud_config_text = self._render_j2_cloud_config(tpl_path, values)
            user_cfg_name = f"J2: {tpl_path}"

            ckpt_payload: Dict[str, Any] = {
                "template_id": template_id,
                "vm_name": vm_name,
                "vcpus": vcpus,
                "ram_gib": ram_gib,
                "use_j2": True,
                "j2_path": str(tpl_path),
                "hostname": hostname,
                "username": username,
                "user_config_id": None,
                "network_config_id": None,
            }

            net_cfg = await self._select_network_config(network_configs)  # optional
            if net_cfg:
                ckpt_payload["network_config_id"] = net_cfg.get("id")

            vifs = self.inventory.resolve_template_vifs(tmpl)

            # Save checkpoint and a timestamped backup (no password stored)
            self._save_checkpoint(ckpt_payload)
            self._save_checkpoint_backup(ckpt_payload)

            await self._summary_and_deploy(
                tmpl, template_id, vm_name, vm_desc, vcpus, ram_gib, memory_bytes,
                vifs, user_cfg_name, cloud_config_text, net_cfg
            )

    # --------- Sub-steps ---------
    async def _select_template(self) -> Optional[Dict[str, Any]]:
        templates = self.inventory.list_templates(NAME_PREFIX)
        return term_pick(templates, lambda t: f"{t.get('name_label')}  (ID {t.get('id')})", "📦 Pick a Template")

    async def _select_user_config(self, user_configs: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        return term_pick(user_configs, lambda c: f"{c['name']}  (cloud-config)", "🧩 Pick User Config (cloud-config) from saved configs")

    async def _select_network_config(self, network_configs: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        return term_pick(network_configs, lambda c: f"{c['name']}  (network-config)", "🌐 Pick Network Config (optional; press Enter to skip)", allow_skip=True)

    async def _set_vm_memory(self, vm_id: str, memory_bytes: int) -> None:
        """Adjust VM memory limits post-create in a safe order (best-effort)."""
        target = int(memory_bytes)

        # Helper to get the latest VM object
        async def _get_vm() -> Dict[str, Any]:
            try:
                objs = await self.client.call("xo.getAllObjects")
                return objs.get(vm_id, {}) or {}
            except Exception:
                return {}

        def _get_int(vm: Dict[str, Any], *keys: str) -> Optional[int]:
            for k in keys:
                v = vm.get(k)
                if isinstance(v, int):
                    return v
            return None

        async def _vm_set(fields: Dict[str, int]) -> bool:
            try:
                await self.client.call("vm.set", {"id": vm_id, **fields})
                return True
            except Exception:
                return False

        any_success = False

        # Fetch current limits
        vm = await _get_vm()
        cur_dyn_min = _get_int(vm, "memoryMin", "memoryDynamicMin")
        cur_dyn_max = _get_int(vm, "memoryMax", "memoryDynamicMax")
        cur_stat_min = _get_int(vm, "memoryStaticMin")
        cur_stat_max = _get_int(vm, "memoryStaticMax")

        # 1) Lower staticMin first (safe: staticMin <= dynamicMin)
        if cur_stat_min is None or cur_stat_min > target:
            if await _vm_set({"memoryStaticMin": target}):
                any_success = True

        # 2) Raise staticMax if needed to accommodate dynamicMax/target
        smx_target = max(filter(None, [target, cur_dyn_max or 0]))  # max of target and current dyn max
        vm = await _get_vm()
        cur_stat_max = _get_int(vm, "memoryStaticMax") or cur_stat_max
        if cur_stat_max is None or cur_stat_max < smx_target:
            if await _vm_set({"memoryStaticMax": smx_target}):
                any_success = True

        # 3) Set dynamic min/max together; fallback to "memory"
        if await _vm_set({"memoryMin": target, "memoryMax": target}):
            any_success = True
        else:
            if await _vm_set({"memory": target}):
                any_success = True

        # 4) Tighten staticMax to target if above
        vm = await _get_vm()
        cur_stat_max = _get_int(vm, "memoryStaticMax") or cur_stat_max
        if cur_stat_max is None or cur_stat_max != target:
            if await _vm_set({"memoryStaticMax": target}):
                any_success = True

        # 5) Ensure staticMin equals target (final touch)
        if await _vm_set({"memoryStaticMin": target}):
            any_success = True

        print("🧠 Memory adjusted." if any_success else "⚠ Skipped memory tuning due to constraints.")

    async def _summary_and_deploy(
        self,
        tmpl: Dict[str, Any],
        template_id: str,
        vm_name: str,
        vm_desc: str,
        vcpus: int,
        ram_gib: int,
        memory_bytes: int,
        vifs: List[Dict[str, str]],
        user_cfg_name: str,
        cloud_config_text: str,
        net_cfg: Optional[Dict[str, Any]],
    ) -> None:

        # Summary
        print("\n📋 DEPLOYMENT SUMMARY")
        print(f"Template: {tmpl.get('name_label')} ({template_id})")
        print(f"VM Name: {vm_name}")
        print(f"Description: {vm_desc}")
        print(f"vCPUs: {vcpus}")
        print(f"RAM: {ram_gib} GiB")
        print("VIFs:")
        for v in vifs:
            net_id = v.get("network")
            net_label = self.inventory.get_network_label(net_id)
            print(f"  - Network: {net_label or '(unknown)'}  (ID: {net_id or 'missing'})  MAC: {v.get('mac', '(auto)')}")
        print(f"User Config: {user_cfg_name}")
        print(f"Network Config: {net_cfg['name'] if net_cfg else '(none)'}")
        proceed = confirm("Proceed with deployment?")
        if not proceed:
            print("🚫 Deployment cancelled.")
            return

        # Build vm.create params (disks come from template)
        params: Dict[str, Any] = {
            "name_label": vm_name,
            "name_description": vm_desc,
            "template": template_id,
            "CPUs": vcpus,
            "coresPerSocket": 1,
            "VIFs": vifs,
            "cloudConfig": cloud_config_text,
        }
        if net_cfg:
            params["networkConfig"] = net_cfg["template"]

        # Create VM (avoid sending memory limits here to prevent constraint violations)
        print("\n🚀 Creating VM...")
        result = await self.client.call("vm.create", params)
        new_vm_id = self._extract_id(result)
        print(f"✅ VM created: {vm_name} (ID: {new_vm_id})")

        # Adjust memory after creation, before starting (best-effort)
        await self._set_vm_memory(new_vm_id, memory_bytes)

        # Auto-start the VM (no prompt)
        try:
            await self.client.call("vm.start", {"id": new_vm_id})
            print("▶ VM started.")
        except Exception as e:
            print(f"⚠ VM created but failed to auto-start: {e}")

# ====================== ENTRYPOINT ======================
async def main():
    async with XOClient(XO_HOST, XO_USER, KEYRING_SERVICE) as client:
        inventory = Inventory(client)
        repo = ConfigRepo(client)
        deployer = VMDeployer(client, inventory, repo)
        await inventory.refresh()
        action = term_pick(["Deploy", "Delete"], lambda a: a, "Select Action")
        if not action:
            print("Cancelled.")
            return
        if action == "Deploy":
            await deployer.run()
        elif action == "Delete":
            # Hide protected VMs from the deletion list
            vms = [v for v in inventory.list_vms() if v.get("id") not in PROTECTED_VM_IDS]
            selected_vms = await multi_pick(
                vms,
                lambda v: f"{v.get('name_label')}  (ID {v.get('id')})",
                "Pick VMs to Delete (Space to select, Enter to confirm)"
            )
            if not selected_vms:
                print("Cancelled.")
                return

            # Final safety: remove any protected ID if somehow selected
            selected_vms = [v for v in selected_vms if v.get("id") not in PROTECTED_VM_IDS]
            if not selected_vms:
                print("🚫 All selected VMs are protected. Nothing to delete.")
                return

            names = ", ".join(v.get("name_label") for v in selected_vms)
            if not confirm(f"Delete {len(selected_vms)} VM(s): {names}? This will delete disks."):
                print("🚫 Deletion cancelled.")
                return

            # Delete each selected VM, report per-VM result
            for v in selected_vms:
                vm_id = v.get("id")
                vm_name = v.get("name_label")
                try:
                    await client.call("vm.delete", {"id": vm_id, "deleteDisks": True})
                    print(f"✅ VM '{vm_name}' deleted with disks.")
                except Exception as e:
                    print(f"⚠ Failed to delete VM '{vm_name}': {e}")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nCancelled.")