#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import asyncio
import json
import ssl
import websockets
import getpass
import keyring
from typing import Any, Dict, List, Optional, Tuple
from pathlib import Path

# ====================== SETTINGS ======================
XO_HOST = "*************"            # XO host (no scheme)
XO_USER = "<EMAIL>"
KEYRING_SERVICE = "xen_orchestra"

# Filter templates by prefix; set to "" for all
NAME_PREFIX = ""

# Where we persist the last-used config
CHECKPOINT_PATH = Path("vm_deploy_checkpoint.json")

def confirm(prompt: str) -> bool:
    """Safely ask for confirmation, requiring full input to prevent accidental key press issues."""
    while True:
        response = input(f"{prompt} [y/n]: ").strip().lower()
        if response in ("y", "yes"):
            return True
        elif response in ("n", "no"):
            return False
        print("Please enter 'y' or 'n'.")

# ====================== UTILITIES ======================
def ask(prompt: str, default: Optional[str] = None) -> str:
    # Use full input() to get the entire line, not just first keypress
    s = input(f"{prompt}{f' [{default}]' if default is not None else ''}: ").strip()
    # Only return default if the user pressed Enter without typing anything
    return s if s else (default or "")

def ask_int(prompt: str, default: int) -> int:
    s = input(f"{prompt} [{default}]: ").strip()
    return int(s) if s else default

def bytes_from_gib(n: int) -> int:
    return int(n * (1024 ** 3))

def term_pick(items: List[Any], label_fn, title: str, allow_skip: bool = False) -> Optional[Any]:
    print(f"\n{title}")
    if not items:
        print("  (none)")
        return None
    for i, it in enumerate(items, 1):
        print(f"  {i}. {label_fn(it)}")
    prompt = f"Select (1-{len(items)})"
    if allow_skip:
        prompt += " or Enter to skip"
    prompt += ", or 'q' to cancel: "
    while True:
        s = input(prompt).strip().lower()
        if s in ("q", "quit"):
            return None
        if allow_skip and s == "":
            return None
        try:
            idx = int(s)
            if 1 <= idx <= len(items):
                return items[idx - 1]
        except Exception:
            pass
        print("Invalid choice.")

# ====================== CLASS 1: CLIENT ======================
class XOClient:
    """Handles connection, auth, and JSON-RPC calls to Xen Orchestra."""
    def __init__(self, host: str, user: str, keyring_service: str):
        self.host = host
        self.user = user
        self.keyring_service = keyring_service
        self.ws: Optional[websockets.WebSocketClientProtocol] = None
        self.ssl_ctx = ssl._create_unverified_context()  # XO often uses self-signed certs
        self._next_id = 1  # RPC id counter

    async def __aenter__(self):
        await self.connect()
        await self.sign_in()
        return self

    async def __aexit__(self, exc_type, exc, tb):
        await self.close()

    async def connect(self):
        uri = f"wss://{self.host}/api/"
        self.ws = await websockets.connect(uri, ssl=self.ssl_ctx)

    async def close(self):
        if self.ws:
            await self.ws.close()
            self.ws = None

    async def call(self, method: str, params: Optional[Dict[str, Any]] = None) -> Any:
        """
        Robust JSON-RPC: assign a unique id, send, then read frames until we get
        the response with our id. Ignore push/event frames like {'method':'all',...}.
        """
        if not self.ws:
            raise RuntimeError("WebSocket not connected.")

        rpc_id = self._next_id
        self._next_id += 1

        payload = {"id": rpc_id, "jsonrpc": "2.0", "method": method, "params": params or {}}
        await self.ws.send(json.dumps(payload))

        while True:
            raw = await self.ws.recv()
            try:
                resp = json.loads(raw)
            except Exception:
                # Unknown frame, keep waiting
                continue

            # Event/push frames (e.g., task updates)
            if isinstance(resp, dict) and "method" in resp and resp.get("method") == "all":
                continue

            # We only care about the frame matching our id
            if isinstance(resp, dict) and resp.get("id") == rpc_id:
                if "error" in resp:
                    raise RuntimeError(f"{method} failed: {resp['error']}")
                if "result" in resp:
                    return resp["result"]
                raise RuntimeError(f"{method} unexpected response: {resp}")

            # Frames for other in-flight calls: ignore
            continue

    async def sign_in(self):
        """
        Sign in using keyring-stored password if available.
        If XO returns code 3 (auth failed), clear keyring and re-prompt.
        """
        pw = keyring.get_password(self.keyring_service, self.user)
        first_attempt = True
        while True:
            if not pw:
                pw = getpass.getpass(f"Password for {self.user}: ")
            try:
                await self.call("session.signIn", {"email": self.user, "password": pw})
                print(f"✅ Logged in as {self.user}")
                # Save to keyring if not already
                if first_attempt and keyring.get_password(self.keyring_service, self.user) is None:
                    keyring.set_password(self.keyring_service, self.user, pw)
                    print("🔑 Password saved to system keyring.")
                return
            except RuntimeError as e:
                msg = str(e)
                bad_creds = ("'code': 3" in msg) or ('"code": 3' in msg)
                if bad_creds:
                    try:
                        keyring.delete_password(self.keyring_service, self.user)
                    except keyring.errors.PasswordDeleteError:
                        pass
                    print("❌ Invalid credentials. Please re-enter password.")
                    pw = None
                    first_attempt = False
                    continue
                raise  # other errors bubble up

# ====================== CLASS 2: INVENTORY ======================
class Inventory:
    """Loads the XO object graph, filters templates, and resolves VIFs for a template."""
    def __init__(self, client: XOClient):
        self.client = client
        self.objects: Dict[str, Dict[str, Any]] = {}

    async def refresh(self):
        self.objects = await self.client.call("xo.getAllObjects")

    def list_templates(self, prefix: str = "") -> List[Dict[str, Any]]:
        temps = [o for o in self.objects.values() if o.get("type") == "VM-template"]
        if prefix:
            pre = prefix.lower()
            temps = [t for t in temps if (t.get("name_label") or "").lower().startswith(pre)]
        return sorted(temps, key=lambda t: (t.get("name_label") or "").lower())

    def get_network_label(self, net_id: Optional[str]) -> str:
        if not net_id:
            return ""
        net = self.objects.get(net_id)
        return net.get("name_label") if net else (net_id or "")

    def resolve_template_vifs(self, template_obj: Dict[str, Any]) -> List[Dict[str, str]]:
        """
        Return VIFs for vm.create: [{'network': '<id: str>', 'mac': '<mac?>'}, ...]
        Handle both 'network' and '$network' ref styles.
        """
        res: List[Dict[str, str]] = []
        for vif_id in template_obj.get("VIFs", []):
            vif = self.objects.get(vif_id)
            if vif and vif.get("type") == "VIF":
                net_id = vif.get("network") or vif.get("$network")
                entry: Dict[str, str] = {"network": str(net_id) if net_id else ""}
                mac = vif.get("MAC")
                if mac:
                    entry["mac"] = mac
                res.append(entry)
        return res

# ====================== CLASS 3: CONFIG REPO ======================
class ConfigRepo:
    """Fetches saved configs (cloudConfig.getAll) and classifies user vs network."""
    def __init__(self, client: XOClient):
        self.client = client

    @staticmethod
    def _classify_text(text: str) -> str:
        lower = (text or "").strip().lower()
        if lower.startswith("#cloud-config"):
            return "cloud-config"
        if lower.startswith("#network") or lower.startswith("#network-config"):
            return "network-config"
        if "network:" in lower and ("version: 2" in lower or any(k in lower for k in ("ethernets:", "vlans:", "bonds:", "bridges:", "wifis:"))):
            return "network-config"
        if "version:" in lower and "config:" in lower and any(k in lower for k in ("type: dhcp", "type: static", "routes:", "nameserver", "address:", "subnets:")):
            return "network-config"
        return "cloud-config"

    async def fetch(self) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
        """Returns (user_configs, network_configs). Each item has id,name,template,type."""
        configs = await self.client.call("cloudConfig.getAll", {})
        user, net = [], []
        for c in configs:
            name = c.get("name", c.get("name_label", "unnamed"))
            template_text = c.get("template", "") or ""
            ctype = self._classify_text(template_text)
            item = {"id": c.get("id"), "name": name, "template": template_text, "type": ctype}
            (user if ctype == "cloud-config" else net).append(item)
        user.sort(key=lambda x: x["name"].lower())
        net.sort(key=lambda x: x["name"].lower())
        return user, net

    @staticmethod
    def find_by_id(configs: List[Dict[str, Any]], config_id: str) -> Optional[Dict[str, Any]]:
        for c in configs:
            if c.get("id") == config_id:
                return c
        return None

# ====================== CLASS 4: DEPLOYER ======================
class VMDeployer:
    """Handles checkpoint, prompts, summary, vm.create, and auto-start."""
    def __init__(self, client: XOClient, inventory: Inventory, repo: ConfigRepo):
        self.client = client
        self.inventory = inventory
        self.repo = repo

    # --------- Checkpoint helpers ---------
    def _save_checkpoint(self, data: Dict[str, Any]) -> None:
        try:
            CHECKPOINT_PATH.write_text(json.dumps(data, indent=2))
        except Exception as e:
            print(f"⚠ Could not save checkpoint: {e}")

    def _load_checkpoint(self) -> Optional[Dict[str, Any]]:
        if not CHECKPOINT_PATH.exists():
            return None
        try:
            return json.loads(CHECKPOINT_PATH.read_text())
        except Exception as e:
            print(f"⚠ Could not read checkpoint: {e}")
            return None

    @staticmethod
    def _extract_id(result) -> str:
        """XO may return {'id': '<uuid>'} or just '<uuid>'."""
        if isinstance(result, dict) and "id" in result:
            return str(result["id"])
        return str(result)

    # --------- Main flow ---------
    async def run(self):
        # Refresh objects
        await self.inventory.refresh()

        # Load saved configs once
        user_configs, network_configs = await self.repo.fetch()

        # Try checkpoint
        ckpt = self._load_checkpoint()
        use_ckpt = False
        if ckpt:
            print("\n💾 Found previous configuration checkpoint.")
            use_ckpt = ask("Use previous configuration? [Y/n]", "Y").lower() in ("y", "yes")

        if use_ckpt:
            # Resolve template from checkpoint
            template_id = ckpt.get("template_id")
            tmpl = self.inventory.objects.get(template_id)
            if not tmpl:
                print("⚠ Template in checkpoint not found; falling back to selection.")
                tmpl = await self._select_template()
                if not tmpl:
                    print("Cancelled.")
                    return
                template_id = tmpl["id"]
            else:
                print(f"📦 Using template from checkpoint: {tmpl.get('name_label')} ({template_id})")

            # VM Name (still editable)
            default_vm_name = ckpt.get("vm_name") or tmpl.get("name_label", "vm-from-template")
            vm_name = ask("VM Name", default_vm_name)
            vm_desc = vm_name  # description = name

            # CPUs/RAM from checkpoint
            vcpus = int(ckpt.get("vcpus", 4))
            ram_gib = int(ckpt.get("ram_gib", 4))
            memory_bytes = bytes_from_gib(ram_gib)

            # User config from checkpoint (must exist)
            user_cfg_id = ckpt.get("user_config_id")
            user_cfg = self.repo.find_by_id(user_configs, user_cfg_id) if user_cfg_id else None
            if not user_cfg:
                print("⚠ User config in checkpoint not found; please select.")
                user_cfg = await self._select_user_config(user_configs)
                if not user_cfg:
                    print("Cancelled.")
                    return

            # Network config from checkpoint (optional)
            net_cfg = None
            net_cfg_id = ckpt.get("network_config_id")
            if net_cfg_id:
                net_cfg = self.repo.find_by_id(network_configs, net_cfg_id)
                if not net_cfg:
                    print("⚠ Network config in checkpoint not found; skipping.")

            # Rebuild VIFs from the (current) template
            vifs = self.inventory.resolve_template_vifs(tmpl)
            await self._summary_and_deploy(tmpl, template_id, vm_name, vm_desc, vcpus, ram_gib, memory_bytes, vifs, user_cfg, net_cfg)

        else:
            # Fresh interactive flow
            tmpl = await self._select_template()
            if not tmpl:
                print("Cancelled.")
                return
            template_id = tmpl["id"]
            default_vm_name = tmpl.get("name_label", "vm-from-template")
            vm_name = ask("VM Name", default_vm_name)
            vm_desc = vm_name

            vcpus = ask_int("vCPUs", 4)
            ram_gib = ask_int("RAM GiB", 4)
            memory_bytes = bytes_from_gib(ram_gib)

            user_cfg = await self._select_user_config(user_configs)
            if not user_cfg:
                print("Cancelled (user config is required).")
                return

            net_cfg = await self._select_network_config(network_configs)  # optional

            vifs = self.inventory.resolve_template_vifs(tmpl)

            # Save checkpoint BEFORE deploy
            self._save_checkpoint({
                "template_id": template_id,
                "vm_name": vm_name,           # still editable next run
                "vcpus": vcpus,
                "ram_gib": ram_gib,
                "user_config_id": user_cfg.get("id"),
                "network_config_id": net_cfg.get("id") if net_cfg else None,
            })

            await self._summary_and_deploy(tmpl, template_id, vm_name, vm_desc, vcpus, ram_gib, memory_bytes, vifs, user_cfg, net_cfg)

    # --------- Sub-steps ---------
    async def _select_template(self) -> Optional[Dict[str, Any]]:
        templates = self.inventory.list_templates(NAME_PREFIX)
        return term_pick(templates, lambda t: f"{t.get('name_label')}  (ID {t.get('id')})", "📦 Pick a Template")

    async def _select_user_config(self, user_configs: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        return term_pick(user_configs, lambda c: f"{c['name']}  (cloud-config)", "🧩 Pick User Config (cloud-config) from saved configs")

    async def _select_network_config(self, network_configs: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        return term_pick(network_configs, lambda c: f"{c['name']}  (network-config)", "🌐 Pick Network Config (optional; press Enter to skip)", allow_skip=True)

    async def _summary_and_deploy(
        self,
        tmpl: Dict[str, Any],
        template_id: str,
        vm_name: str,
        vm_desc: str,
        vcpus: int,
        ram_gib: int,
        memory_bytes: int,
        vifs: List[Dict[str, str]],
        user_cfg: Dict[str, Any],
        net_cfg: Optional[Dict[str, Any]],
    ) -> None:

        # Summary
        print("\n📋 DEPLOYMENT SUMMARY")
        print(f"Template: {tmpl.get('name_label')} ({template_id})")
        print(f"VM Name: {vm_name}")
        print(f"Description: {vm_desc}")
        print(f"vCPUs: {vcpus}")
        print(f"RAM: {ram_gib} GiB")
        print("VIFs:")
        for v in vifs:
            net_id = v.get("network")
            net_label = self.inventory.get_network_label(net_id)
            print(f"  - Network: {net_label or '(unknown)'}  (ID: {net_id or 'missing'})  MAC: {v.get('mac', '(auto)')}")
        print(f"User Config: {user_cfg['name']}")
        print(f"Network Config: {net_cfg['name'] if net_cfg else '(none)'}")
        confirm = confirm("Proceed with deployment?")
        # Make sure we don't auto-accept the first keypress
        if not confirm:
            print("🚫 Deployment cancelled.")
            return

        # Build vm.create params (disks come from template)
        params: Dict[str, Any] = {
            "name_label": vm_name,
            "name_description": vm_desc,
            "template": template_id,
            "CPUs": vcpus,
            "memory": memory_bytes,
            "coresPerSocket": 1,
            "VIFs": vifs,
            "cloudConfig": user_cfg["template"],
        }
        if net_cfg:
            params["networkConfig"] = net_cfg["template"]

        # Create VM
        print("\n🚀 Creating VM...")
        result = await self.client.call("vm.create", params)
        new_vm_id = self._extract_id(result)
        print(f"✅ VM created: {vm_name} (ID: {new_vm_id})")

        # Auto-start the VM (no prompt)
        try:
            await self.client.call("vm.start", {"id": new_vm_id})
            print("▶ VM started.")
        except Exception as e:
            print(f"⚠ VM created but failed to auto-start: {e}")

# ====================== ENTRYPOINT ======================
async def main():
    async with XOClient(XO_HOST, XO_USER, KEYRING_SERVICE) as client:
        inventory = Inventory(client)
        repo = ConfigRepo(client)
        deployer = VMDeployer(client, inventory, repo)
        await deployer.run()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nCancelled.")
