#cloud-config
hostname: node-01
fqdn: node-01.local
preserve_hostname: false
ssh_pwauth: true

users:
  - name: admin
    shell: /bin/bash
    lock_passwd: false
    sudo: ['ALL=(ALL) NOPASSWD:ALL']
    plain_text_passwd: yourpassword
    chpasswd: { expire: False }

# Do not let cloud-init auto-write a conflicting DHCP netplan
network:
  config: disabled

packages:
  - avahi-daemon
  - libnss-mdns
  - openssh-server

write_files:
  # Our DHCP netplan with clean routing: primary has default route, secondary no routes
  - path: /etc/netplan/99-dhcp.yaml
    owner: root:root
    permissions: '0600'
    content: |
      network:
        version: 2
        renderer: networkd
        ethernets:
          primary:
            match: { name: "en*0" }        # first NIC (mgmt/API)
            dhcp4: true
            optional: true
            dhcp4-overrides:
              route-metric: 100           # prefer this NIC for default route
              send-hostname: true
          secondary:
            match: { name: "en*1" }        # second NIC (storage/overlay)
            dhcp4: true
            optional: true
            dhcp4-overrides:
              use-routes: false           # avoid second default route
  # Make sure mDNS is enabled in resolved
  - path: /etc/systemd/resolved.conf.d/mdns.conf
    owner: root:root
    permissions: '0644'
    content: |
      [Resolve]
      MulticastDNS=yes
      LLMNR=no
  # Ensure NSS looks up .local via mDNS first (common default, but enforce it)
  - path: /etc/nsswitch.conf
    owner: root:root
    permissions: '0644'
    content: |
      passwd:         files systemd
      group:          files systemd
      shadow:         files
      gshadow:        files
      hosts:          files mdns4_minimal [NOTFOUND=return] dns myhostname
      networks:       files
      protocols:      db files
      services:       db files
      ethers:         db files
      rpc:            db files
      netgroup:       nis

bootcmd:
  # Remove any cloud-init netplan if it slipped in
  - [ bash, -lc, 'rm -f /etc/netplan/50-cloud-init.yaml || true' ]

runcmd:
  - [ bash, -lc, 'mkdir -p /etc/systemd/resolved.conf.d' ]
  - [ bash, -lc, 'systemctl enable --now systemd-networkd systemd-resolved avahi-daemon' ]
  - [ bash, -lc, 'netplan --debug generate' ]
  - [ bash, -lc, 'netplan apply' ]
  - [ bash, -lc, 'systemctl restart ssh || true' ]
