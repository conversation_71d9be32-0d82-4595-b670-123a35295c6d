#!/usr/bin/env python3
"""
EKS-Anywhere OS prep controller (Ubuntu 20.04/22.04).
Orchestrates prerequisite installation, validates setup, prints a Markdown table summary with pandas,
writes a JSON checkpoint file, sets up NFS for per-cluster storage, manages SSH service, and configures system settings.
Must be run as root (sudo).
"""
import os
import subprocess
import json
import sys
import pandas as pd

class SystemController:
    def __init__(self):
        self.versions = {}
        self.packages = [
            ("bash", ["bash", "--version"]),
            ("containerd", ["containerd", "--version"]),
            ("git", ["git", "--version"]),
            ("curl", ["curl", "--version"]),
            ("wget", ["wget", "--version"]),
            ("jq", ["jq", "--version"]),
            ("dnsutils", ["dig", "--version"]),
            ("iproute2", ["ip", "--version"]),
            ("net-tools", ["netstat", "--version"]),
            ("python3", ["python3", "--version"]),
            ("pip3", ["pip3", "--version"]),
            ("openssh-server", ["dpkg-query", "--showformat=${Version}", "--show", "openssh-server"]),
            ("docker", ["docker", "--version"]),
            ("helm", ["helm", "version"]),
            ("kubectl", ["kubectl", "version", "--client"]),
            ("eksctl", ["eksctl", "version"]),
            ("eksctl-anywhere", ["eksctl-anywhere", "version"]),
            ("yq", ["yq", "--version"]),
            ("nfs-common", ["dpkg-query", "--showformat=${Version}", "--show", "nfs-common"]),
            ("xfsprogs", ["dpkg-query", "--showformat=${Version}", "--show", "xfsprogs"]),
            ("e2fsprogs", ["dpkg-query", "--showformat=${Version}", "--show", "e2fsprogs"]),
            ("parted", ["parted", "--version"]),
            ("xe-guest-utilities", ["dpkg-query", "--showformat=${Version}", "--show", "xe-guest-utilities"]),
            ("cloud-initramfs-growroot", ["dpkg-query", "--showformat=${Version}", "--show", "cloud-initramfs-growroot"]),
            ("jinja2", ["python3", "-c", "import jinja2; print(jinja2.__version__)"]),
            ("pyyaml", ["python3", "-c", "import yaml; print(yaml.__version__)"]),
            ("requests", ["python3", "-c", "import requests; print(requests.__version__)"]),
            ("pandas", ["python3", "-c", "import pandas; print(pandas.__version__)"]),
            ("tabulate", ["python3", "-c", "import tabulate; print(tabulate.__version__)"]),
            ("nfs-kernel-server", ["rpcinfo", "-p"]),
        ]
        self.cluster_name = "dev"
        self.host_ip = self._get_host_ip()

    def _get_host_ip(self):
        try:
            result = subprocess.run(["ip", "-4", "addr", "show"], capture_output=True, text=True, check=True)
            for line in result.stdout.splitlines():
                if "inet " in line and "scope global" in line:
                    ip = line.split()[1].split("/")[0]
                    if ip.startswith(("192.168.", "10.", "172.")):
                        return ip
            return "************"
        except Exception:
            return "************"

    def log(self, msg):
        print(f"\033[94m[info]\033[0m {msg}")

    def warn(self, msg):
        print(f"\033[93m[warn]\033[0m {msg}")

    def success(self, msg):
        print(f"\033[92m[✓]\033[0m {msg}")

    def run_cmd(self, cmd, raise_on_error=True, silent=False):
        self.log(f"Running: {' '.join(cmd)}")
        try:
            result = subprocess.run(cmd, check=True, capture_output=True, text=True)
            return result.stdout.strip() if silent else None
        except subprocess.CalledProcessError as e:
            self.warn(f"Command failed: {' '.join(e.cmd)}")
            if e.stderr:
                self.warn(f"Error output: {e.stderr.strip()}")
            if raise_on_error:
                sys.exit(1)
            return None

    def check_package(self, name, check_cmd):
        try:
            res = subprocess.run(check_cmd, capture_output=True, text=True)
            if res.returncode == 0:
                out = (res.stdout or "").strip()
                if not out and res.stderr:
                    out = res.stderr.strip()
                if out:
                    self.versions[name] = out.splitlines()[0]
                    return True
            return False
        except FileNotFoundError:
            return False

    def install_prerequisites(self):
        self.log("Updating package lists and installing basics...")
        self.run_cmd(["apt-get", "update", "-qq"], raise_on_error=True)
        self.run_cmd(["apt-get", "upgrade", "-y", "docker.io"], raise_on_error=False)

        for pkg in ["containerd", "git", "curl", "wget", "jq", "dnsutils", "iproute2", "net-tools", "python3", "python3-pip", "openssh-server", "docker.io", "nfs-common", "xfsprogs", "e2fsprogs", "parted", "xe-guest-utilities", "cloud-initramfs-growroot"]:
            if self.check_package(pkg, ["dpkg-query", "--showformat=${Version}", "--show", pkg]):
                self.warn(f"{pkg} already installed")
                continue
            self.run_cmd(["apt-get", "install", "-y", pkg], raise_on_error=False)

        self.run_cmd(["systemctl", "enable", "--now", "ssh", "docker"], raise_on_error=False)

        if os.environ.get("SUDO_USER"):
            self.run_cmd(["usermod", "-aG", "docker", os.environ["SUDO_USER"]], raise_on_error=False)

        self.log("Configuring containerd...")
        self.run_cmd(["mkdir", "-p", "/etc/containerd"])
        if not os.path.exists("/etc/containerd/config.toml"):
            self.run_cmd(["containerd", "config", "default"], silent=True)
        self.run_cmd(["sed", "-i", "s/SystemdCgroup = false/SystemdCgroup = true/", "/etc/containerd/config.toml"])
        self.run_cmd(["systemctl", "enable", "--now", "containerd", "systemd-timesyncd"], raise_on_error=False)

        self.log("Configuring kernel and sysctl...")
        self.run_cmd(["bash", "-c", "echo -e 'overlay\nbr_netfilter' > /etc/modules-load.d/k8s.conf"])
        self.run_cmd(["modprobe", "overlay"], raise_on_error=False)
        self.run_cmd(["modprobe", "br_netfilter"], raise_on_error=False)
        self.run_cmd(["bash", "-c", "echo -e 'net.ipv4.ip_forward=1\nnet.bridge.bridge-nf-call-iptables=1\nnet.bridge.bridge-nf-call-ip6tables=1' > /etc/sysctl.d/k8s.conf"])
        self.run_cmd(["sysctl", "--system"], raise_on_error=False)

        self.log("Installing helm, kubectl, eksctl, yq...")
        if not self.check_package("helm", ["helm", "version"]):
            self.run_cmd(["curl", "-fsSL", "https://raw.githubusercontent.com/helm/helm/main/scripts/get-helm-3", "|", "bash"], raise_on_error=False)
        if not self.check_package("kubectl", ["kubectl", "version", "--client"]):
            self.run_cmd(["curl", "-LO", "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"])
            self.run_cmd(["install", "-m", "0755", "kubectl", "/usr/local/bin/kubectl"])
            self.run_cmd(["rm", "kubectl"], raise_on_error=False)
        if not self.check_package("eksctl", ["eksctl", "version"]):
            self.run_cmd(["curl", "-sL", "https://github.com/weaveworks/eksctl/releases/latest/download/eksctl_Linux_amd64.tar.gz", "|", "tar", "zx"])
            self.run_cmd(["install", "-m", "0755", "eksctl", "/usr/local/bin/eksctl"])
            self.run_cmd(["rm", "eksctl"], raise_on_error=False)
        if not self.check_package("yq", ["yq", "--version"]):
            self.run_cmd(["wget", "-qO", "/usr/local/bin/yq", "https://github.com/mikefarah/yq/releases/latest/download/yq_linux_amd64"])
            self.run_cmd(["chmod", "+x", "/usr/local/bin/yq"], raise_on_error=False)

        self.log("Installing eksctl-anywhere...")
        try:
            manifest_cmd = ["curl", "-sSL", "https://anywhere-assets.eks.amazonaws.com/releases/eks-a/manifest.yaml"]
            manifest_result = subprocess.run(manifest_cmd, capture_output=True, text=True, check=True)
            version_result = subprocess.run(["yq", ".spec.latestVersion"], input=manifest_result.stdout, capture_output=True, text=True, check=True)
            release_version = version_result.stdout.strip()
            url_result = subprocess.run(
                ["yq", f'.spec.releases[] | select(.version=="{release_version}").eksABinary.linux.uri'],
                input=manifest_result.stdout, capture_output=True, text=True, check=True
            )
            tarball_url = url_result.stdout.strip()
            self.run_cmd(["curl", "-sSL", tarball_url, "-o", "/tmp/eksctl-anywhere.tar.gz"])
            self.run_cmd(["tar", "-xzf", "/tmp/eksctl-anywhere.tar.gz", "-C", "/tmp", "eksctl-anywhere"])
            self.run_cmd(["install", "-m", "0755", "/tmp/eksctl-anywhere", "/usr/local/bin/eksctl-anywhere"])
            self.run_cmd(["rm", "-f", "/tmp/eksctl-anywhere.tar.gz", "/tmp/eksctl-anywhere"])
            self.success("eksctl-anywhere installed successfully")
        except Exception as e:
            self.warn(f"Failed to install eksctl-anywhere: {e}")

        self.log("Installing Python libraries...")
        self.run_cmd(["pip3", "install", "--break-system-packages", "jinja2", "pyyaml", "requests", "pandas", "tabulate"], raise_on_error=False)

    def setup_nfs_storage(self, disk_device="/dev/xvdb"):
        self.log("Setting up NFS storage...")
        try:
            if os.path.exists(disk_device) and not os.path.ismount("/srv/eks"):
                self.run_cmd(["parted", disk_device, "--script", "mklabel", "gpt", "mkpart", "primary", "ext4", "0%", "100%"])
                self.run_cmd(["mkfs.ext4", "-L", "eksdata", f"{disk_device}1"])
                self.run_cmd(["mkdir", "-p", "/srv/eks"])
                uuid = self.run_cmd(["blkid", "-s", "UUID", "-o", "value", f"{disk_device}1"], silent=True)
                if uuid:
                    self.run_cmd(["bash", "-c", f'echo "UUID={uuid} /srv/eks ext4 defaults,noatime 0 2" >> /etc/fstab'])
                self.run_cmd(["mount", "-a"], raise_on_error=False)

            cluster_path = f"/srv/eks/{self.cluster_name}"
            self.run_cmd(["mkdir", "-p", cluster_path])
            self.run_cmd(["chown", "-R", "nobody:nogroup", "/srv/eks"])
            self.run_cmd(["chmod", "-R", "0777", "/srv/eks"])

            self.run_cmd(["apt-get", "install", "-y", "nfs-kernel-server"])
            export_line = f"{cluster_path} ***********/24(rw,sync,no_subtree_check,no_root_squash)"
            escaped_path = cluster_path.replace("/", "\\/")
            self.run_cmd(["sed", "-i", f"/^{escaped_path} /d", "/etc/exports"], raise_on_error=False)
            self.run_cmd(["bash", "-c", f'echo "{export_line}" >> /etc/exports'])
            self.run_cmd(["exportfs", "-ra"])
            self.run_cmd(["systemctl", "enable", "--now", "nfs-kernel-server"])
            self.run_cmd(["showmount", "-e", "localhost"], raise_on_error=False)
            self.success("NFS server setup completed")
        except Exception as e:
            self.warn(f"NFS setup failed: {e}")

    def setup_ssh_service(self):
        self.log("Setting up SSH service...")
        try:
            self.run_cmd(["systemctl", "enable", "--now", "systemd-resolved"], raise_on_error=False)
            if not self.check_package("openssh-server", ["dpkg-query", "--showformat=${Version}", "--show", "openssh-server"]):
                self.run_cmd(["apt-get", "install", "-y", "--no-install-recommends", "openssh-server"])
            self.run_cmd(["truncate", "-s", "0", "/etc/machine-id"], raise_on_error=False)
            self.run_cmd(["find", "/etc/ssh/", "-type", "f", "-name", "ssh_host_*", "-delete"], raise_on_error=False)
            self.run_cmd(["ssh-keygen", "-A"], raise_on_error=False)
            self.run_cmd(["sshd", "-t"], raise_on_error=False)
            self.run_cmd(["systemctl", "daemon-reload"], raise_on_error=False)
            self.run_cmd(["systemctl", "enable", "--now", "ssh"])
            self.success("SSH service configured and started")
        except Exception as e:
            self.warn(f"SSH setup failed: {e}")

    def configure_cloud_init(self):
        self.log("Configuring cloud-init...")
        self.run_cmd(["bash", "-c", 'echo "datasource_list: [NoCloud, ConfigDrive, OpenStack, XenStore]" > /etc/cloud/cloud.cfg.d/99-xcp-ng.cfg'])
        self.run_cmd(["dpkg-reconfigure", "--default-priority", "cloud-init"], raise_on_error=False)
        self.run_cmd(["chmod", "a-x", "/etc/cloud/clean.d/99-installer"], raise_on_error=False)
        self.run_cmd(["rm", "-f", "/etc/cloud/cloud.cfg.d/99-installer.cfg", "/etc/cloud/cloud.cfg.d/subiquity-disable-cloudinit-networking.cfg", "/etc/cloud/cloud.cfg.d/90-installer-network.cfg"], raise_on_error=False)
        self.run_cmd(["cloud-init", "clean", "--logs", "--seed"], raise_on_error=False)
        self.run_cmd(["rm", "-rf", "/var/lib/cloud/instances", "/var/lib/cloud/instance"], raise_on_error=False)
        self.run_cmd(["rm", "-f", "/var/log/cloud-init.log", "/var/log/cloud-init*", "/etc/netplan/00-installer-config.yaml", "/etc/netplan/50-cloud-init.yaml"], raise_on_error=False)
        for svc in ["cloud-init-local.service", "cloud-config.service", "cloud-final.service"]:
            self.run_cmd(["systemctl", "enable", svc], raise_on_error=False)
        self.run_cmd(["systemctl", "enable", "--now", "xe-linux-distribution"], raise_on_error=False) or \
        self.run_cmd(["systemctl", "enable", "--now", "xe-daemon"], raise_on_error=False)

    def disable_swap(self):
        self.log("Disabling swap...")
        self.run_cmd(["swapoff", "-a"], raise_on_error=False)
        self.run_cmd(["sed", "-i", "/swap/d", "/etc/fstab"], raise_on_error=False)

    def validate_installations(self):
        self.log("Validating installations...")
        for name, check_cmd in self.packages:
            if self.check_package(name, check_cmd):
                self.success(f"{name} installed")
            else:
                self.warn(f"{name} not found")
                self.versions[name] = "<missing>"

    def cleanup_users(self):
        self.log("Cleaning up user accounts...")
        keep_users = {
            "root", "daemon", "bin", "sys", "sync", "games", "man", "lp", "mail", "news", "uucp", "proxy", "www-data",
            "backup", "list", "irc", "gnats", "nobody", "systemd-timesync", "systemd-network", "systemd-resolve",
            "messagebus", "syslog", "landscape", "pollinate", "sshd", "systemd-coredump", "lxd", "dnsmasq", "usbmux",
            "rtkit", "cups-pk-helper", "geoclue", "pulse", "gdm", "hplip", "avahi", "colord", "speech-dispatcher",
            "kernoops", "whoopsie", "avahi-autoipd", "uuidd", "lightdm", "systemd-oom", "fwupd-refresh", "tcpdump",
            "_apt", "tss", "admin", "docker", "containerd", "xe-linux-distribution", "cloud-init"
        }
        result = self.run_cmd(["getent", "passwd"], silent=True)
        if not result:
            self.warn("Could not enumerate users")
            return

        users_to_remove = []
        for line in result.split("\n"):
            if ":" in line:
                parts = line.split(":")
                username = parts[0]
                try:
                    uid = int(parts[2])
                except Exception:
                    continue
                if username not in keep_users and (uid >= 1000 or (uid >= 100 and username not in keep_users)):
                    users_to_remove.append(username)

        for username in users_to_remove:
            self.log(f"Removing user: {username}")
            self.run_cmd(["userdel", "-r", username], raise_on_error=False)
        self.run_cmd(["rm", "-rf", "/home/<USER>"], raise_on_error=False)
        self.success("User cleanup completed")

    def generate_report(self):
        self.log("Generating installation report...")
        df = pd.DataFrame.from_dict(self.versions, orient="index", columns=["Version"])
        df.index.name = "Package"
        print(df.to_markdown())
        checkpoint = {
            "k8s_configured": True,
            "nfs_configured": True,
            "ssh_configured": True,
            "versions": self.versions,
            "nfs_details": {
                "host_ip": self.host_ip,
                "export_path": f"/srv/eks/{self.cluster_name}",
                "storage_class": "dev-nfs",
            },
        }
        with open("os_prep_status.json", "w") as f:
            json.dump(checkpoint, f, indent=2)
        self.success("Checkpoint written: os_prep_status.json")

if __name__ == "__main__":
    if os.geteuid() != 0:
        print("❌ Run as root: sudo python3 os_prep_combined.py")
        sys.exit(1)

    controller = SystemController()
    controller.run_cmd(["systemctl", "enable", "--now", "systemd-resolved"], raise_on_error=False)
    controller.install_prerequisites()
    controller.configure_cloud_init()
    controller.disable_swap()
    controller.setup_nfs_storage()
    controller.setup_ssh_service()
    controller.validate_installations()
    controller.cleanup_users()
    controller.generate_report()
    controller.success("OS prep complete. Restarting...")
    controller.run_cmd(["shutdown", "-r", "now"])