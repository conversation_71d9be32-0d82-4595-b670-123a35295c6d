#!/usr/bin/env python3
import re
from pathlib import Path

def extract_j2_defaults(template_path):
    try:
        content = template_path.read_text()
    except Exception as e:
        print(f"Error reading file: {e}")
        return {}
    
    print("Template content:")
    print(content[:500])
    print("\n" + "="*50 + "\n")
    
    defaults = {}
    for var in ['hostname', 'fqdn', 'username', 'password']:
        # Look for patterns like {{ var | default('value') }}
        pattern = r"{{\s*" + re.escape(var) + r"\s*\|\s*default\(['\"](.*?)['\"]\)\s*}}"
        print(f"Searching for variable: {var}")
        print(f"Pattern: {pattern}")
        
        match = re.search(pattern, content)
        if match:
            defaults[var] = match.group(1)
            print(f"✅ Found default: {match.group(1)}")
        else:
            print(f"❌ No match found")
        print()
    
    return defaults

if __name__ == "__main__":
    template_path = Path('config_template/cloud-init-admin-host.yaml.j2')
    print(f"Testing regex extraction on: {template_path}")
    defaults = extract_j2_defaults(template_path)
    print(f"\nFinal extracted defaults: {defaults}")
